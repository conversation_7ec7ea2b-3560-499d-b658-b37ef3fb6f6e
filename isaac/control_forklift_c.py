from isaacsim.simulation_app import SimulationApp
import numpy as np

# Isaac Sim app configuration
CONFIG = {
    "renderer": "RayTracedLighting",
    "headless": False,
}

# Start the simulation app
kit = SimulationApp(CONFIG)

# Post-startup imports
from omni.isaac.core import World
from omni.isaac.core.objects import GroundPlane
from omni.isaac.core.utils.stage import add_reference_to_stage
from omni.isaac.core.articulations import Articulation
from omni.isaac.core.utils.nucleus import get_assets_root_path
from isaacsim.robot.wheeled_robots.controllers.ackermann_controller import A<PERSON>mannController
# Make sure the local project root is on sys.path so `import isaac.*` works when running inside Isaac Sim
import os
import sys
_this_dir = os.path.abspath(os.path.dirname(__file__))
_project_root = os.path.abspath(os.path.join(_this_dir, ".."))
if _project_root not in sys.path:
    sys.path.insert(0, _project_root)

from isaac.pure_pursuit import pure_pursuit_steer, stanley_steer, VelocityPID, example_straight_path, quaternion_to_yaw, <PERSON><PERSON><PERSON><PERSON><PERSON>, find_lookahead_point, find_nearest_index
import omni.usd
from pxr import UsdGeom, Gf, Sdf

world = World()
world.scene.add(GroundPlane(prim_path="/World/groundPlane", size=1000, color=np.array([0.5, 0.5, 0.5])))

# --- Load the Forklift ---
assets_root_path = get_assets_root_path()
if assets_root_path is None:
    raise Exception("Could not find Isaac Sim assets folder.")

forklift_usd_path = "https://omniverse-content-production.s3-us-west-2.amazonaws.com/Assets/Isaac/4.5/Isaac/Robots/Forklift/forklift_c.usd"
print(f"Attempting to load model: {forklift_usd_path}")
add_reference_to_stage(usd_path=forklift_usd_path, prim_path="/World/Forklift")

forklift = Articulation(prim_path="/World/Forklift", name="forklift_robot")
world.scene.add(forklift)

# --- Ackermann Controller Setup ---
# These values should be adjusted based on the actual robot's geometry
wheel_base = 1.65  # Distance between front and rear axles (m)
track_width = 0.82  # Distance between left and right rear wheels (m)
turning_wheel_radius = 0.255  # Radius of the turning wheels (m)
max_wheel_velocity = 20.0  # Maximum angular velocity of the wheels (rad/s)
max_wheel_rotation = 0.69813  # Maximum wheel rotation angle (rad)
max_steering_angle = 0.69813  # Maximum steering angle (rad)

# Additional control parameters
invert_steering_angle = True  # Whether to invert the steering angle

# Note: front_wheel_radius is used for backward compatibility with AckermannController
front_wheel_radius = turning_wheel_radius

ackermann_controller = AckermannController(
    "forklift_ackermann_controller",
    wheel_base=wheel_base,
    track_width=track_width,
    front_wheel_radius=front_wheel_radius
    # Removed max_wheel_velocity and max_steering_angle as they are not constructor arguments
)

# Prepare trajectory and controllers for Pure Pursuit
# First test with a simple straight line to diagnose control issues
use_straight_path = False  # Toggle between straight and curved path

if use_straight_path:
    # Simple straight line path for debugging
    xs = np.linspace(0.0, 30.0, 300)
    ys = np.zeros_like(xs)  # straight line along x-axis
    path = np.vstack([xs, ys]).T
    print("Using straight line path for debugging")
else:
    # Create a much gentler test path - very gradual S curve
    xs = np.linspace(0.0, 50.0, 500)
    # Use an extremely gentle curve to prevent overshoot
    ys = 1.5 * np.sin(0.5 * xs)  # very small amplitude and low frequency
    path = np.vstack([xs, ys]).T
    print("Using very gentle curved sine path")

# optional: visualize first few points in log
print("Loaded curved test path, first points:", path[:6])

# Visualization helpers: write path as Points prim (fast) and optionally spheres for debugging
def visualize_path_as_points(path_np: np.ndarray, prim_path="/World/PlannedPathPoints", z=0.05, width=0.02):
    try:
        stage = omni.usd.get_context().get_stage()
        if stage is None:
            print("USD stage not available for visualization.")
            return
        # remove existing prim if present
        if stage.GetPrimAtPath(Sdf.Path(prim_path)):
            stage.RemovePrim(Sdf.Path(prim_path))
        pts = UsdGeom.Points.Define(stage, Sdf.Path(prim_path))
        points3 = [Gf.Vec3f(float(x), float(y), float(z)) for x, y in path_np]
        pts.CreatePointsAttr(points3)
        widths = [float(width)] * len(points3)
        pts.CreateWidthsAttr(widths)
        print(f"Visualized path as Points at {prim_path} ({len(points3)} points)")
    except Exception as e:
        print(f"Failed to visualize path as points: {e}")

def visualize_path_as_spheres(path_np: np.ndarray, parent_path="/World/PlannedPath", z=0.05, radius=0.12, max_points=200):
    try:
        stage = omni.usd.get_context().get_stage()
        if stage is None:
            print("USD stage not available for visualization.")
            return
        if stage.GetPrimAtPath(Sdf.Path(parent_path)):
            stage.RemovePrim(Sdf.Path(parent_path))
        parent = UsdGeom.Xform.Define(stage, Sdf.Path(parent_path))
        # limit number of spheres to avoid heavy scene
        for i, (x, y) in enumerate(path_np[:max_points]):
            prim_path = f"{parent_path}/pt_{i:04d}"
            s = UsdGeom.Sphere.Define(stage, Sdf.Path(prim_path))
            s.GetRadiusAttr().Set(radius)
            s.AddTranslateOp().Set(Gf.Vec3d(float(x), float(y), float(z)))
        print(f"Visualized up to {min(len(path_np), max_points)} spheres at {parent_path}")
    except Exception as e:
        print(f"Failed to visualize path as spheres: {e}")

# Try to visualize path once (stage should be available after World/kit started)
visualize_path_as_points(path, prim_path="/World/PlannedPathPoints", z=0.05, width=0.02)
# optionally also create a few spheres for clearer inspection
visualize_path_as_spheres(path, parent_path="/World/PlannedPath", z=0.05, radius=0.05, max_points=150)

# Adaptive lookahead for curves - increase significantly for better stability
lookahead_base = 4.0  # further increased for curve stability
lookahead_gain = 0.05  # adaptive based on speed
velocity_pid = VelocityPID(kp=1.0, ki=0.0, kd=0.0)
# More aggressive smoothing for curves to prevent overshoot
steering_filter = SteeringFilter(alpha=0.15, rate_limit=0.3)  # reduced alpha, reduced rate_limit
prev_pos = None

# Use Pure Pursuit instead of Stanley for smoother path following
use_stanley = False  # switch to Pure Pursuit
stanley_k = 0.15  # reduced gain for Stanley (if used later)
stanley_k_soft = 0.3  # increased softening

# --- Main Simulation Loop ---
initialized = False
left_drive_joint_idx = -1
right_drive_joint_idx = -1
left_steering_joint_idx = -1
right_steering_joint_idx = -1
lift_joint_idx = -1

# Control variables
desired_speed = 20   
desired_steering_angle = 0.0  # Desired steering angle (rad)

while kit.is_running():
    world.step(render=True)
    
    if not world.is_playing():
        if initialized:
            initialized = False
        continue
        
    if not initialized:
        world.reset()
        # Check if the forklift has joints
        print(f"Forklift has {forklift.num_dof} degrees of freedom (joints)")
        
        # Try to get joint information
        if forklift.num_dof > 0:
            print("Attempting to get joint properties...")
            try:
                # Print dof_properties structure
                print(f"dof_properties type: {type(forklift.dof_properties)}")
                print(f"dof_properties: {forklift.dof_properties}")
            except Exception as e:
                print(f"Could not get dof_properties. Error: {e}")
        
        # Try to get joint names for better understanding
        try:
            joint_names = forklift.dof_names
            print(f"Joint names: {joint_names}")
        except:
            print("Could not get joint names")
        
        # Based on dof_properties analysis, let's try this mapping:

        # Joints 5 and 6 are likely the drive wheels (unlimited rotation)
        left_drive_joint_idx = 5
        right_drive_joint_idx = 6
        # Joints 2 and 3 have steering limits (±0.785 rad)
        left_steering_joint_idx = 2
        right_steering_joint_idx = 3
        # Joint 4 has 0-2.0 range (lift)
        lift_joint_idx = 4
        # print(f"New mapping - Left Drive: {left_drive_joint_idx}, Right Drive: {right_drive_joint_idx}, Left Steering: {left_steering_joint_idx}, Right Steering: {right_steering_joint_idx}, Lift: {lift_joint_idx}")
        initialized = True


    if initialized:
        import math
        time_step = world.current_time_step_index

        # Get current world pose (position + orientation) first
        try:
            wp = forklift.get_world_pose()
            if isinstance(wp, (tuple, list)) and len(wp) >= 2:
                pos, quat = wp[0], wp[1]
            else:
                # fallback if API differs
                pos = (0.0, 0.0, 0.0)
                quat = (0.0, 0.0, 0.0, 1.0)
            current_pos = (pos[0], pos[1])
            current_yaw = quaternion_to_yaw((quat[0], quat[1], quat[2], quat[3]))
        except Exception:
            current_pos = (0.0, 0.0)
            current_yaw = 0.0

        # Adaptive speed based on curvature - slower for tighter curves
        base_speed = 1
        # Calculate path curvature at current position for speed adaptation
        try:
            nearest_idx = find_nearest_index(path, current_pos)
            if nearest_idx < len(path) - 2:
                # Estimate local curvature using three consecutive points
                p1 = path[max(0, nearest_idx-1)]
                p2 = path[nearest_idx]
                p3 = path[min(len(path)-1, nearest_idx+1)]
                # Simple curvature approximation
                v1 = p2 - p1
                v2 = p3 - p2
                cross = np.cross(v1, v2)
                curvature_magnitude = abs(cross) / (np.linalg.norm(v1) * np.linalg.norm(v2) + 1e-6)
                # Reduce speed proportionally to curvature
                speed_factor = max(0.2, 1.0 - 2.0 * curvature_magnitude)
                desired_speed = base_speed * speed_factor
            else:
                desired_speed = base_speed
        except:
            desired_speed = base_speed
        
        desired_speed = max(1.0, min(desired_speed, base_speed))  # clamp between 1.0 and base_speed

        # Estimate current speed using previous position; define dt for filtering/rate limits
        dt = 1.0 / 60.0
        current_speed = desired_speed
        if prev_pos is not None:
            dist = np.linalg.norm(np.array(current_pos) - np.array(prev_pos))
            current_speed = dist / max(dt, 1e-6)
        prev_pos = current_pos

        # Adaptive lookahead
        lookahead = lookahead_base + lookahead_gain * max(0.0, current_speed)

        # Compute steering using Pure Pursuit (rear-axle reference) with diagnostics
        try:
            # rear axle reference: shift current_pos backward by lf along heading
            lf = wheel_base / 2.0
            rear_x = current_pos[0] - lf * math.cos(current_yaw)
            rear_y = current_pos[1] - lf * math.sin(current_yaw)
            rear_pos = (rear_x, rear_y)

            nearest_idx = find_nearest_index(path, rear_pos)
            target_pt = find_lookahead_point(path, rear_pos, lookahead)
            dx_dbg = float(target_pt[0] - rear_pos[0])
            dy_dbg = float(target_pt[1] - rear_pos[1])
            # vehicle-frame components (use vehicle yaw)
            cos_y = math.cos(-current_yaw)
            sin_y = math.sin(-current_yaw)
            local_x = cos_y * dx_dbg - sin_y * dy_dbg
            local_y = sin_y * dx_dbg + cos_y * dy_dbg
            alpha = math.atan2(local_y, local_x) if (abs(local_x) + abs(local_y)) > 1e-9 else 0.0
            curvature_dbg = 2.0 * math.sin(alpha) / max(lookahead, 1e-6)

            # compute nearest path tangent, path_yaw, cross-track error and yaw error (relative to rear_pos)
            path_yaw = 0.0
            cross_track = 0.0
            try:
                idx2 = min(nearest_idx + 1, len(path) - 1)
                seg = path[idx2] - path[nearest_idx]
                path_yaw = math.atan2(float(seg[1]), float(seg[0]))
                rel = np.array(rear_pos) - path[nearest_idx]
                rot = np.array([[math.cos(-path_yaw), -math.sin(-path_yaw)],
                                [math.sin(-path_yaw),  math.cos(-path_yaw)]])
                rel_path = rot.dot(rel)
                cross_track = float(rel_path[1])
            except Exception:
                pass

            # signed yaw error (wrap to [-pi,pi])
            def _wrap(a):
                return (a + math.pi) % (2 * math.pi) - math.pi
            yaw_err = _wrap(path_yaw - current_yaw)

            print(f"[PP DEBUG REAR] lf={lf:.2f}, rear=({rear_pos[0]:.2f},{rear_pos[1]:.2f}), nearest_idx={nearest_idx}, target=({target_pt[0]:.2f},{target_pt[1]:.2f}), "
                  f"local=({local_x:.2f},{local_y:.2f}), alpha={alpha:.2f}, curvature={curvature_dbg:.2f}, lookahead={lookahead:.2f}, "
                  f"path_yaw={path_yaw:.2f}, yaw_err={yaw_err:.2f}, cross_track={cross_track:.2f}")
        except Exception as _e:
            print(f"[PP DEBUG REAR] failed diagnostics: {_e}")
        # compute raw steering from both controllers for comparison, then select and apply
        try:
            pp_raw = float(pure_pursuit_steer(rear_pos, current_yaw, path, lookahead, wheel_base, max_steering_angle))
        except Exception as _e:
            pp_raw = 0.0
            print(f"[PP DEBUG REAR] pure_pursuit failed: {_e}")

        try:
            stan_raw = float(stanley_steer(rear_pos, current_yaw, path, speed=max(0.0, current_speed),
                                           k=stanley_k, k_soft=stanley_k_soft, wheel_base=wheel_base,
                                           max_steer=max_steering_angle))
        except Exception as _e:
            stan_raw = 0.0
            print(f"[PP DEBUG REAR] stanley failed: {_e}")

        # choose controller output
        raw_steer = stan_raw if use_stanley else pp_raw

        filtered_steer = raw_steer
        # Apply invertSteeringAngle if enabled (apply to raw for diagnostics)
        if invert_steering_angle:
            raw_steer = -raw_steer

        # Limit steering angle to maximum value (clamp after inversion)
        raw_steer = float(np.clip(raw_steer, -max_steering_angle, max_steering_angle))

        # Apply steering smoothing filter to reduce overshoot (uses dt defined above)
        try:
            filtered_steer = steering_filter.apply(raw_steer, dt)
        except Exception:
            # If filter fails for any reason, keep raw steering
            filtered_steer = raw_steer

        desired_steering_angle = filtered_steer

        # Diagnostic print: show both controller raw outputs and selected / filtered
        print(f"[PP VS STAN] pp_raw={pp_raw:.3f}, stan_raw={stan_raw:.3f}, sel_raw={raw_steer:.3f}, filtered={filtered_steer:.3f}, applied={desired_steering_angle:.3f}")

        # Calculate wheel rotation velocity
        wheel_rotation_velocity = desired_speed / front_wheel_radius
        
        # Limit wheel velocity to maximum value
        wheel_rotation_velocity = np.clip(wheel_rotation_velocity, -max_wheel_velocity, max_wheel_velocity)
        

        
        # Both wheels turn same direction as desired
        left_wheel_angle = desired_steering_angle
        right_wheel_angle = desired_steering_angle
        
        # Limit wheel rotation angles to maximum value
        left_wheel_angle = np.clip(left_wheel_angle, -max_wheel_rotation, max_wheel_rotation)
        right_wheel_angle = np.clip(right_wheel_angle, -max_wheel_rotation, max_wheel_rotation)
        
        print(f"Steering: {desired_steering_angle:.2f}, Left: {left_wheel_angle:.2f}, Right: {right_wheel_angle:.2f}")

        
        # Apply the computed actions to the robot
        joint_velocities = np.zeros(forklift.num_dof)
        
        # Set drive wheel velocities
        joint_velocities[left_drive_joint_idx] = wheel_rotation_velocity
        joint_velocities[right_drive_joint_idx] = wheel_rotation_velocity

        # Print debug info every 50 steps
        if time_step % 50 == 0:
            print(f"Step {time_step}: Speed={desired_speed}, Steering={desired_steering_angle:.2f}, "
                  f"Left_angle={left_wheel_angle:.2f}, Right_angle={right_wheel_angle:.2f}")
            print(f"Joint velocities: {joint_velocities}")
        
        forklift.set_joint_velocities(joint_velocities)
        
        # Control steering and lift
        current_positions = forklift.get_joint_positions()
        if current_positions is not None:
            current_positions[left_steering_joint_idx] = left_wheel_angle
            current_positions[right_steering_joint_idx] = right_wheel_angle
            
            # Simple lift control (same as before)
            lift_position = 1.0 # + 0.2 * math.sin(time_step * 0.03)
            lift_position = np.clip(lift_position, 0.0, 2.0)
            current_positions[lift_joint_idx] = lift_position
            
            forklift.set_joint_positions(current_positions)

print("Simulation has been closed.")
kit.close()
