import mujoco
import mujoco.viewer
import numpy as np
import time
import math

class MuJoCoLearningDemo:
    """
    MuJoCo 学习演示类 - 包含各种功能来帮助学习 MuJoCo
    """
    
    def __init__(self, xml_path='mujoco_menagerie/universal_robots_ur5e/ur5e.xml'):
        """初始化模型和数据"""
        print("🤖 初始化 MuJoCo 学习演示...")
        
        # 加载模型
        self.model = mujoco.MjModel.from_xml_path(xml_path)
        self.data = mujoco.MjData(self.model)
        
        # 演示模式
        self.demo_mode = 0
        self.demo_modes = [
            "基础信息显示",
            "正弦波控制",
            "随机运动",
            "重力补偿",
            "关节空间控制",
            "末端执行器控制",
            "碰撞检测演示",
            "传感器数据读取",
            "约束演示",
            "能量分析"
        ]
        
        # 控制参数
        self.time_step = 0
        self.control_freq = 0.5  # 控制频率
        
        # 数据记录
        self.position_history = []
        self.velocity_history = []
        self.force_history = []
        
        self.print_model_info()
    
    def print_model_info(self):
        """打印模型基础信息"""
        print("\n" + "="*60)
        print("📊 模型信息概览")
        print("="*60)
        print(f"模型名称: {self.model.names[0] if self.model.names else '未命名'}")
        print(f"关节数量: {self.model.nq}")
        print(f"驱动器数量: {self.model.nu}")
        print(f"刚体数量: {self.model.nbody}")
        print(f"几何体数量: {self.model.ngeom}")
        print(f"传感器数量: {self.model.nsensor}")
        print(f"时间步长: {self.model.opt.timestep}")
        
        print("\n📝 关节信息:")
        for i in range(self.model.nq):
            joint_name = mujoco.mj_id2name(self.model, mujoco.mjtObj.mjOBJ_JOINT, i)
            joint_type = self.model.jnt_type[i]
            print(f"  关节 {i}: {joint_name} (类型: {joint_type})")
        
        if self.model.nu > 0:
            print("\n🎮 驱动器信息:")
            for i in range(self.model.nu):
                actuator_name = mujoco.mj_id2name(self.model, mujoco.mjtObj.mjOBJ_ACTUATOR, i)
                print(f"  驱动器 {i}: {actuator_name}")
        
        print("="*60)
    
    def basic_info_display(self):
        """演示模式 0: 基础信息显示"""
        if self.time_step % 100 == 0:  # 每100步显示一次
            print(f"\n⏰ 时间: {self.data.time:.2f}s")
            print(f"📍 关节位置: {np.round(self.data.qpos, 3)}")
            print(f"💨 关节速度: {np.round(self.data.qvel, 3)}")
            if self.model.nu > 0:
                print(f"🔧 控制输入: {np.round(self.data.ctrl, 3)}")
            print(f"⚡ 系统能量: {self.calculate_total_energy():.3f} J")
    
    def sine_wave_control(self):
        """演示模式 1: 正弦波控制"""
        if self.model.nu > 0:
            # 为每个驱动器生成不同频率的正弦波
            for i in range(min(self.model.nu, self.model.nq)):
                frequency = 0.5 + i * 0.2  # 不同关节使用不同频率
                amplitude = 0.5  # 振幅
                self.data.ctrl[i] = amplitude * math.sin(frequency * self.data.time)
            
            if self.time_step % 50 == 0:
                print(f"🌊 正弦波控制 - 时间: {self.data.time:.2f}s")
                print(f"   控制信号: {np.round(self.data.ctrl, 3)}")
    
    def random_motion(self):
        """演示模式 2: 随机运动"""
        if self.model.nu > 0 and self.time_step % 20 == 0:  # 每20步更新一次
            # 生成随机控制信号
            for i in range(self.model.nu):
                self.data.ctrl[i] = np.random.uniform(-1.0, 1.0)
            
            if self.time_step % 100 == 0:
                print(f"🎲 随机运动 - 控制信号: {np.round(self.data.ctrl, 3)}")
    
    def gravity_compensation(self):
        """演示模式 3: 重力补偿"""
        if self.model.nu > 0:
            # 计算重力补偿力矩
            mujoco.mj_inverse(self.model, self.data)
