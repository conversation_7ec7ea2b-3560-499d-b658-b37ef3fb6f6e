"""
MuJoCo 简单演示脚本
包含基础功能演示，适合初学者
"""

import mujoco
import mujoco.viewer
import numpy as np
import math

def simple_basic_demo():
    """简单基础演示"""
    print("🤖 MuJoCo 简单演示")
    
    # 加载模型
    model_path = 'mujoco_menagerie/universal_robots_ur5e/ur5e.xml'
    model = mujoco.MjModel.from_xml_path(model_path)
    data = mujoco.MjData(model)
    
    print(f"✅ 模型加载成功: {model.nq} 个关节, {model.nu} 个驱动器")
    
    # 设置初始位置
    if model.nq > 0:
        data.qpos[:] = 0.1 * np.random.randn(model.nq)
    
    # 启动可视化
    with mujoco.viewer.launch_passive(model, data) as viewer:
        step = 0
        while viewer.is_running():
            # 简单正弦波控制
            if model.nu > 0:
                for i in range(model.nu):
                    data.ctrl[i] = 0.3 * math.sin(0.01 * step + i)
            
            # 每1000步打印信息
            if step % 1000 == 0:
                print(f"⏰ 步数: {step}, 时间: {data.time:.2f}s")
                print(f"📍 关节位置: {np.round(data.qpos, 2)}")
            
            # 仿真步进
            mujoco.mj_step(model, data)
            viewer.sync()
            step += 1

def gravity_drop_demo():
    """重力下降演示"""
    print("🌍 重力下降演示")
    
    model_path = 'mujoco_menagerie/universal_robots_ur5e/ur5e.xml'
    model = mujoco.MjModel.from_xml_path(model_path)
    data = mujoco.MjData(model)
    
    # 设置初始位置（抬高机器人）
    if model.nq > 0:
        data.qpos[:] = 0.5 * np.ones(model.nq)
    
    print("🔧 不施加任何控制，观察重力作用下的运动")
    
    with mujoco.viewer.launch_passive(model, data) as viewer:
        step = 0
        while viewer.is_running():
            # 不施加控制力，让机器人自由下落
            if model.nu > 0:
                data.ctrl[:] = 0.0
            
            if step % 500 == 0:
                total_height = np.sum(data.xpos[:, 2])  # 所有刚体的Z坐标和
                print(f"📏 总高度: {total_height:.3f}")
            
            mujoco.mj_step(model, data)
            viewer.sync()
            step += 1

def position_control_demo():
    """位置控制演示"""
    print("🎯 位置控制演示")
    
    model_path = 'mujoco_menagerie/universal_robots_ur5e/ur5e.xml'
    model = mujoco.MjModel.from_xml_path(model_path)
    data = mujoco.MjData(model)
    
    # 目标位置
    target_positions = np.array([0.5, -0.3, 0.8, -0.5, 0.2, 0.1])
    target_positions = target_positions[:model.nu]  # 确保维度匹配
    
    print(f"🎯 目标位置: {target_positions}")
    print("使用简单PD控制器控制机器人到达目标位置")
    
    with mujoco.viewer.launch_passive(model, data) as viewer:
        step = 0
        while viewer.is_running():
            if model.nu > 0:
                # 简单PD控制
                kp = 50.0  # 比例增益
                kd = 5.0   # 微分增益
                
                pos_error = target_positions - data.qpos[:model.nu]
                vel_error = 0 - data.qvel[:model.nu]
                
                data.ctrl[:] = kp * pos_error + kd * vel_error
                
                # 每1000步打印误差
                if step % 1000 == 0:
                    error_norm = np.linalg.norm(pos_error)
                    print(f"📊 位置误差: {error_norm:.4f}")
                    if error_norm < 0.01:
                        print("🎉 到达目标位置!")
            
            mujoco.mj_step(model, data)
            viewer.sync()
            step += 1

def circular_motion_demo():
    """圆周运动演示"""
    print("🔄 圆周运动演示")
    
    model_path = 'mujoco_menagerie/universal_robots_ur5e/ur5e.xml'
    model = mujoco.MjModel.from_xml_path(model_path)
    data = mujoco.MjData(model)
    
    print("让第一个关节做圆周运动，其他关节保持固定")
    
    with mujoco.viewer.launch_passive(model, data) as viewer:
        step = 0
        while viewer.is_running():
            if model.nu > 0:
                # 第一个关节做圆周运动
                data.ctrl[0] = 0.5 * math.sin(0.01 * step)
                
                # 其他关节使用PD控制保持在0位置
                if model.nu > 1:
                    kp = 20.0
                    kd = 2.0
                    for i in range(1, model.nu):
                        pos_error = 0 - data.qpos[i]
                        vel_error = 0 - data.qvel[i]
                        data.ctrl[i] = kp * pos_error + kd * vel_error
                
                if step % 1000 == 0:
                    print(f"🔄 第一关节位置: {data.qpos[0]:.3f}")
            
            mujoco.mj_step(model, data)
            viewer.sync()
            step += 1

def energy_monitoring_demo():
    """能量监控演示"""
    print("⚡ 能量监控演示")
    
    model_path = 'mujoco_menagerie/universal_robots_ur5e/ur5e.xml'
    model = mujoco.MjModel.from_xml_path(model_path)
    data = mujoco.MjData(model)
    
    # 给系统一些初始能量
    if model.nq > 0:
        data.qvel[:] = 0.1 * np.random.randn(model.nq)
    
    print("监控系统能量变化")
    
    with mujoco.viewer.launch_passive(model, data) as viewer:
        step = 0
        energy_history = []
        
        while viewer.is_running():
            # 计算能量
            M = np.zeros((model.nv, model.nv))
            mujoco.mj_fullM(model, M, data.qM)
            kinetic_energy = 0.5 * data.qvel.T @ M @ data.qvel
            
            # 计算势能
            potential_energy = 0.0
            for i in range(model.nbody):
                mass = model.body_mass[i]
                height = data.xpos[i][2]
                potential_energy += mass * 9.81 * height
            
            total_energy = kinetic_energy + potential_energy
            energy_history.append(total_energy)
            
            # 不施加控制，观察能量守恒
            if model.nu > 0:
                data.ctrl[:] = 0.0
            
            if step % 1000 == 0:
                print(f"⚡ 动能: {kinetic_energy:.4f}, 势能: {potential_energy:.4f}, 总能量: {total_energy:.4f}")
                
                if len(energy_history) > 1000:
                    energy_std = np.std(energy_history[-1000:])
                    print(f"📊 最近1000步能量标准差: {energy_std:.6f}")
            
            mujoco.mj_step(model, data)
            viewer.sync()
            step += 1

def interactive_demo_menu():
    """交互式演示菜单"""
    demos = {
        '1': ('基础演示', simple_basic_demo),
        '2': ('重力下降', gravity_drop_demo),
        '3': ('位置控制', position_control_demo),
        '4': ('圆周运动', circular_motion_demo),
        '5': ('能量监控', energy_monitoring_demo)
    }
    
    print("\n🎮 MuJoCo 简单演示程序")
    print("="*40)
    print("请选择演示:")
    for key, (name, _) in demos.items():
        print(f"  {key}. {name}")
    print("  q. 退出")
    print("="*40)
    
    while True:
        try:
            choice = input("\n请输入选择 (1-5/q): ").strip().lower()
            
            if choice == 'q':
                print("👋 再见!")
                break
            elif choice in demos:
                name, func = demos[choice]
                print(f"\n🚀 启动 {name}...")
                func()
            else:
                print("❌ 无效选择，请重试")
        
        except KeyboardInterrupt:
            print("\n👋 程序被中断，退出")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")
            print("请确保模型文件存在")

if __name__ == "__main__":
    interactive_demo_menu()
