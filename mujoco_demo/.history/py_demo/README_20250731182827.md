# MuJoCo 学习演示脚本说明

本目录包含了多个 MuJoCo 学习演示脚本，旨在帮助您深入学习 MuJoCo 的各种功能和概念。

## 📁 文件说明

### 1. `py_demo1.py` - 原始基础脚本
您的原始脚本，包含最基本的 MuJoCo 设置。

### 2. `py_demo1_enhanced.py` - 完整功能演示
**适用人群**: 想要全面了解 MuJoCo 功能的用户

**特点**:
- 面向对象设计，代码结构清晰
- 10种不同的演示模式，自动循环切换
- 实时显示详细的系统信息
- 包含高级功能如末端执行器控制、能量分析等

**运行方式**:
```bash
python py_demo1_enhanced.py
```

**包含的演示模式**:
- 🔍 基础信息显示 - 实时显示关节状态、能量等
- 🌊 正弦波控制 - 多频率正弦波控制演示
- 🎲 随机运动 - 随机控制信号演示
- 🪐 重力补偿 - 计算并应用重力补偿
- 🎯 关节空间控制 - PD控制器实现精确位置控制
- 🤖 末端执行器控制 - 雅可比矩阵控制末端轨迹
- 💥 碰撞检测演示 - 实时显示接触信息
- 📡 传感器数据读取 - 读取和显示传感器数据
- 🔗 约束演示 - 显示系统约束信息
- ⚡ 能量分析 - 动能、势能、总能量计算

### 3. `py_demo_concepts.py` - 概念式学习
**适用人群**: 想要系统学习 MuJoCo 核心概念的用户

**特点**:
- 交互式学习体验
- 按概念分类，可以单独学习每个概念
- 详细的理论解释和实践演示
- 可以选择学习特定概念或全部概念

**运行方式**:
```bash
python py_demo_concepts.py
```

**核心概念**:
1. 📋 模型检查 - 了解模型结构和属性
2. 📊 状态检查 - 系统状态信息读取
3. 🚀 正向动力学 - 从控制输入到系统响应
4. ⬅️ 逆向动力学 - 从期望运动到所需力矩
5. ⚖️ 质量矩阵 - 系统惯性特性分析
6. 🔗 雅可比矩阵 - 关节空间到操作空间的映射
7. 💥 接触检测 - 碰撞和接触机制
8. 📡 传感器数据 - 各种传感器的使用
9. 🎮 基础控制 - PD控制器原理和实现
10. ⚡ 能量分析 - 系统能量的计算和分析

### 4. `py_demo_simple.py` - 简化入门演示
**适用人群**: MuJoCo 初学者

**特点**:
- 简单易懂的代码结构
- 每个演示都专注于一个核心概念
- 适合循序渐进的学习

**运行方式**:
```bash
python py_demo_simple.py
```

**演示内容**:
1. 🤖 基础演示 - 最基本的仿真和控制
2. 🌍 重力下降 - 理解重力作用
3. 🎯 位置控制 - 基础PD控制器
4. 🔄 圆周运动 - 单关节运动控制
5. ⚡ 能量监控 - 能量守恒原理

## 🚀 快速开始

### 环境要求
- Python 3.7+
- MuJoCo 库
- NumPy

### 安装依赖
```bash
pip install mujoco numpy
```

### 选择适合的脚本
- **完全新手**: 先运行 `py_demo_simple.py`
- **有基础经验**: 运行 `py_demo_concepts.py` 学习核心概念
- **想要全面了解**: 运行 `py_demo1_enhanced.py` 查看所有功能

## 🎮 使用技巧

### 查看器控制
- **鼠标左键拖拽**: 旋转视角
- **鼠标右键拖拽**: 平移视角
- **滚轮**: 缩放
- **空格键**: 暂停/继续仿真
- **R键**: 重置仿真

### 学习建议
1. **从简单开始**: 建议先运行 `py_demo_simple.py` 了解基础概念
2. **逐步深入**: 使用 `py_demo_concepts.py` 系统学习每个概念
3. **综合应用**: 最后运行 `py_demo1_enhanced.py` 看到所有功能的综合应用
4. **代码学习**: 阅读代码注释，理解每个功能的实现原理
5. **实验修改**: 尝试修改参数，观察不同的效果

## 🔧 自定义和扩展

### 更换机器人模型
在脚本中修改模型路径：
```python
model_path = 'mujoco_menagerie/franka_emika_panda/panda.xml'  # 更换为其他机器人
```

### 调整控制参数
修改PD控制器参数：
```python
kp = 50.0  # 比例增益
kd = 5.0   # 微分增益
```

### 添加新功能
参考现有代码结构，添加您自己的演示功能。

## 📚 学习资源

- [MuJoCo 官方文档](https://mujoco.readthedocs.io/)
- [MuJoCo Python 绑定](https://github.com/deepmind/mujoco)
- [机器人学基础知识](https://robotics.stanford.edu/)

## ❓ 常见问题

**Q: 运行时提示找不到模型文件**
A: 确保 `mujoco_menagerie` 目录存在且包含相应的模型文件

**Q: 仿真运行很慢**
A: 可以调整 `model.opt.timestep` 或减少显示更新频率

**Q: 想要学习特定概念**
A: 使用 `py_demo_concepts.py` 并选择对应的概念编号

**Q: 如何保存仿真数据**
A: 在代码中添加数据记录功能，将 `data.qpos`, `data.qvel` 等保存到文件

## 🤝 贡献

欢迎提出改进建议和添加新的演示功能！

---

祝您学习愉快！🎉
