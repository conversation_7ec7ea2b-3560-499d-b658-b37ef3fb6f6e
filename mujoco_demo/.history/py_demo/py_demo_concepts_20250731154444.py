"""
MuJoCo 概念学习脚本
按概念分类的学习示例，每个函数演示一个特定概念
"""

import mujoco
import mujoco.viewer
import numpy as np
import time
import math

# 全局变量
model = None
data = None

def load_model(xml_path='mujoco_menagerie/universal_robots_ur5e/ur5e.xml'):
    """加载模型和数据"""
    global model, data
    print(f"📥 加载模型: {xml_path}")
    model = mujoco.MjModel.from_xml_path(xml_path)
    data = mujoco.MjData(model)
    print("✅ 模型加载完成")

def concept_1_model_inspection():
    """概念1: 模型检查 - 了解模型的基本属性"""
    print("\n" + "="*50)
