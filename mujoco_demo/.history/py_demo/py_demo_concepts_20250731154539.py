"""
MuJoCo 概念学习脚本
按概念分类的学习示例，每个函数演示一个特定概念
"""

import mujoco
import mujoco.viewer
import numpy as np
import time
import math

# 全局变量
model = None
data = None

def load_model(xml_path='mujoco_menagerie/universal_robots_ur5e/ur5e.xml'):
    """加载模型和数据"""
    global model, data
    print(f"📥 加载模型: {xml_path}")
    model = mujoco.MjModel.from_xml_path(xml_path)
    data = mujoco.MjData(model)
    print("✅ 模型加载完成")

def concept_1_model_inspection():
    """概念1: 模型检查 - 了解模型的基本属性"""
    print("\n" + "="*50)
    print("📋 概念1: 模型检查")
    print("="*50)
    
    print(f"模型名称: {getattr(model, 'name', '未命名')}")
    print(f"时间步长: {model.opt.timestep}")
    print(f"重力: {model.opt.gravity}")
    
    print(f"\n📊 自由度信息:")
    print(f"  位置坐标数 (nq): {model.nq}")
    print(f"  速度坐标数 (nv): {model.nv}")
    print(f"  驱动器数 (nu): {model.nu}")
    
    print(f"\n🏗️ 物理实体:")
    print(f"  刚体数: {model.nbody}")
    print(f"  关节数: {model.njnt}")
    print(f"  几何体数: {model.ngeom}")
    print(f"  传感器数: {model.nsensor}")
    
    print(f"\n🔧 关节详情:")
    for i in range(model.njnt):
        joint_name = mujoco.mj_id2name(model, mujoco.mjtObj.mjOBJ_JOINT, i)
        joint_type = model.jnt_type[i]
        print(f"  关节 {i}: {joint_name or f'joint_{i}'} (类型: {joint_type})")

def concept_2_state_inspection():
    """概念2: 状态检查 - 了解系统当前状态"""
    print("\n" + "="*50)
    print("📊 概念2: 状态检查")
    print("="*50)
    
    print(f"当前时间: {data.time:.3f}s")
    print(f"关节位置 (qpos): {np.round(data.qpos, 3)}")
    print(f"关节速度 (qvel): {np.round(data.qvel, 3)}")
    print(f"关节加速度 (qacc): {np.round(data.qacc, 3)}")
    
    if model.nu > 0:
        print(f"控制输入 (ctrl): {np.round(data.ctrl, 3)}")
    
    print(f"\n🌍 刚体位置和方向:")
    for i in range(min(3, model.nbody)):  # 只显示前3个刚体
        body_name = mujoco.mj_id2name(model, mujoco.mjtObj.mjOBJ_BODY, i)
        pos = data.xpos[i]
        quat = data.xquat[i]
        print(f"  {body_name or f'body_{i}'}: 位置={np.round(pos, 3)}, 四元数={np.round(quat, 3)}")

def concept_3_forward_dynamics():
    """概念3: 正向动力学 - 从控制输入计算加速度"""
    print("\n" + "="*50)
    print("🚀 概念3: 正向动力学")
    print("="*50)
    
    # 设置一些控制输入
    if model.nu > 0:
        data.ctrl[:] = 0.1 * np.sin(data.time * np.arange(model.nu))
        print(f"设置控制输入: {np.round(data.ctrl, 3)}")
    
    # 计算正向动力学
    mujoco.mj_forward(model, data)
    
    print(f"计算得到的加速度: {np.round(data.qacc, 3)}")
    print(f"关节合力: {np.round(data.qfrc_applied, 3)}")

def concept_4_inverse_dynamics():
    """概念4: 逆向动力学 - 从期望加速度计算所需力矩"""
    print("\n" + "="*50)
    print("⬅️ 概念4: 逆向动力学")
    print("="*50)
    
    # 设置期望加速度
    data.qacc[:] = 0.1 * np.sin(data.time + np.arange(model.nv))
    print(f"期望加速度: {np.round(data.qacc, 3)}")
    
    # 计算逆向动力学
    mujoco.mj_inverse(model, data)
    
    print(f"所需关节力矩: {np.round(data.qfrc_inverse, 3)}")

def concept_5_mass_matrix():
    """概念5: 质量矩阵 - 了解系统的惯性特性"""
    print("\n" + "="*50)
    print("⚖️ 概念5: 质量矩阵")
    print("="*50)
    
    # 计算质量矩阵
    M = np.zeros((model.nv, model.nv))
    mujoco.mj_fullM(model, M, data.qM)
    
    print(f"质量矩阵形状: {M.shape}")
    print(f"质量矩阵对角线: {np.round(np.diag(M), 3)}")
    print(f"质量矩阵条件数: {np.linalg.cond(M):.2f}")
    
    # 计算动能
    kinetic_energy = 0.5 * data.qvel.T @ M @ data.qvel
    print(f"当前动能: {kinetic_energy:.4f} J")

def concept_6_jacobian():
    """概念6: 雅可比矩阵 - 关节速度到末端执行器速度的映射"""
    print("\n" + "="*50)
    print("🔗 概念6: 雅可比矩阵")
    print("="*50)
    
    # 选择末端执行器 (通常是最后一个刚体)
    end_effector_id = model.nbody - 1
    end_effector_name = mujoco.mj_id2name(model, mujoco.mjtObj.mjOBJ_BODY, end_effector_id)
    print(f"末端执行器: {end_effector_name or f'body_{end_effector_id}'}")
    
    # 计算位置雅可比
    jacp = np.zeros((3, model.nv))
    jacr = np.zeros((3, model.nv))
    mujoco.mj_jac(model, data, jacp, jacr, data.xpos[end_effector_id], end_effector_id)
    
    print(f"位置雅可比矩阵形状: {jacp.shape}")
    print(f"旋转雅可比矩阵形状: {jacr.shape}")
    
    # 计算末端执行器速度
    end_effector_vel = jacp @ data.qvel
    end_effector_angular_vel = jacr @ data.qvel
    
    print(f"末端执行器线速度: {np.round(end_effector_vel, 3)}")
    print(f"末端执行器角速度: {np.round(end_effector_angular_vel, 3)}")

def concept_7_contacts():
    """概念7: 接触检测 - 理解碰撞和接触"""
    print("\n" + "="*50)
    print("💥 概念7: 接触检测")
    print("="*50)
    
    print(f"接触数量: {data.ncon}")
    
    if data.ncon > 0:
        for i in range(min(data.ncon, 3)):  # 最多显示3个接触
            contact = data.contact[i]
            geom1_name = mujoco.mj_id2name(model, mujoco.mjtObj.mjOBJ_GEOM, contact.geom1)
            geom2_name = mujoco.mj_id2name(model, mujoco.mjtObj.mjOBJ_GEOM, contact.geom2)
            
            print(f"\n接触 {i}:")
            print(f"  几何体1: {geom1_name or contact.geom1}")
            print(f"  几何体2: {geom2_name or contact.geom2}")
            print(f"  接触位置: {np.round(contact.pos, 3)}")
            print(f"  接触法向量: {np.round(contact.frame[:3], 3)}")
            print(f"  接触距离: {contact.dist:.4f}")
    else:
        print("当前无接触")

def concept_8_sensors():
    """概念8: 传感器 - 读取各种传感器数据"""
    print("\n" + "="*50)
    print("📡 概念8: 传感器数据")
    print("="*50)
    
    if model.nsensor > 0:
        print(f"传感器数量: {model.nsensor}")
        
        for i in range(model.nsensor):
            sensor_name = mujoco.mj_id2name(model, mujoco.mjtObj.mjOBJ_SENSOR, i)
            sensor_type = model.sensor_type[i]
            
            # 获取传感器数据
            adr = model.sensor_adr[i]
            dim = model.sensor_dim[i]
            sensor_data = data.sensordata[adr:adr+dim]
            
            print(f"\n传感器 {i}: {sensor_name or f'sensor_{i}'}")
            print(f"  类型: {sensor_type}")
            print(f"  数据: {np.round(sensor_data, 3)}")
    else:
        print("模型中没有传感器")

def concept_9_control_basics():
    """概念9: 基础控制 - PD控制示例"""
    print("\n" + "="*50)
    print("🎮 概念9: 基础控制 (PD控制)")
    print("="*50)
    
    if model.nu == 0:
        print("该模型没有驱动器，无法演示控制")
        return
    
    # 设置目标位置
    target_pos = 0.5 * np.sin(0.5 * data.time + np.arange(model.nq))
    target_pos = target_pos[:model.nu]  # 确保维度匹配
    
    # PD控制参数
    kp = 100.0  # 比例增益
    kd = 10.0   # 微分增益
    
    # 计算控制输入
    pos_error = target_pos - data.qpos[:model.nu]
    vel_error = 0 - data.qvel[:model.nu]  # 目标速度为0
    
    control_input = kp * pos_error + kd * vel_error
    data.ctrl[:] = control_input
    
    print(f"目标位置: {np.round(target_pos, 3)}")
    print(f"当前位置: {np.round(data.qpos[:model.nu], 3)}")
    print(f"位置误差: {np.round(pos_error, 3)}")
    print(f"控制输入: {np.round(control_input, 3)}")

def concept_10_energy():
    """概念10: 能量分析 - 动能和势能"""
    print("\n" + "="*50)
    print("⚡ 概念10: 能量分析")
    print("="*50)
    
    # 计算动能
    M = np.zeros((model.nv, model.nv))
    mujoco.mj_fullM(model, M, data.qM)
    kinetic_energy = 0.5 * data.qvel.T @ M @ data.qvel
    
    # 计算势能 (重力势能)
    potential_energy = 0.0
    for i in range(model.nbody):
        mass = model.body_mass[i]
        height = data.xpos[i][2]  # Z坐标
        potential_energy += mass * (-model.opt.gravity[2]) * height
    
    total_energy = kinetic_energy + potential_energy
    
    print(f"动能: {kinetic_energy:.4f} J")
    print(f"势能: {potential_energy:.4f} J")
    print(f"总能量: {total_energy:.4f} J")

def run_concept_demo(concept_num):
    """运行指定概念的演示"""
    concepts = {
        1: concept_1_model_inspection,
        2: concept_2_state_inspection,
        3: concept_3_forward_dynamics,
        4: concept_4_inverse_dynamics,
        5: concept_5_mass_matrix,
        6: concept_6_jacobian,
        7: concept_7_contacts,
        8: concept_8_sensors,
        9: concept_9_control_basics,
        10: concept_10_energy
    }
    
    if concept_num in concepts:
        concepts[concept_num]()
    else:
        print(f"❌ 概念 {concept_num} 不存在")

def interactive_learning():
    """交互式学习模式"""
    print("\n🎓 欢迎使用 MuJoCo 概念学习程序!")
    print("="*60)
    print("可用概念:")
    concepts_list = [
        "1. 模型检查",
        "2. 状态检查", 
        "3. 正向动力学",
        "4. 逆向动力学",
        "5. 质量矩阵",
        "6. 雅可比矩阵",
        "7. 接触检测",
        "8. 传感器数据",
        "9. 基础控制",
        "10. 能量分析"
    ]
    
    for concept in concepts_list:
        print(concept)
    
    print("\n使用方法:")
    print("- 输入数字 1-10 运行对应概念演示")
    print("- 输入 'all' 运行所有概念")
    print("- 输入 'sim' 启动可视化仿真")
    print("- 输入 'quit' 退出")
    
    while True:
        try:
            choice = input("\n请选择 (1-10/all/sim/quit): ").strip().lower()
            
            if choice == 'quit':
                print("👋 再见!")
                break
            elif choice == 'all':
                print("\n🔄 运行所有概念演示...")
                for i in range(1, 11):
                    run_concept_demo(i)
                    time.sleep(1)
            elif choice == 'sim':
                print("\n🎮 启动可视化仿真...")
                run_simulation_with_concepts()
                break
            elif choice.isdigit():
                concept_num = int(choice)
                if 1 <= concept_num <= 10:
                    run_concept_demo(concept_num)
                else:
                    print("❌ 请输入 1-10 之间的数字")
            else:
                print("❌ 无效输入，请重试")
                
        except KeyboardInterrupt:
            print("\n👋 程序被中断，退出")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")

def run_simulation_with_concepts():
    """运行带概念演示的可视化仿真"""
    print("🚀 启动可视化仿真，按数字键 1-9,0 切换概念演示")
    
    current_concept = 1
    step_counter = 0
    
    with mujoco.viewer.launch_passive(model, data) as viewer:
        while viewer.is_running():
            # 每隔一段时间运行概念演示
            if step_counter % 200 == 0:
                run_concept_demo(current_concept)
                current_concept = (current_concept % 10) + 1
            
            # 仿真步进
            mujoco.mj_step(model, data)
            viewer.sync()
            step_counter += 1

def main():
    """主函数"""
    # 默认模型路径
    model_path = 'mujoco_menagerie/universal_robots_ur5e/ur5e.xml'
    
    try:
        # 加载模型
        load_model(model_path)
        
        # 启动交互式学习
        interactive_learning()
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        print("请确保模型文件存在且MuJoCo正确安装")

if __name__ == "__main__":
    main()
