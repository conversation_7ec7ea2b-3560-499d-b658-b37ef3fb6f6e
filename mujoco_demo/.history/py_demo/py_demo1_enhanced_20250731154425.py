import mujoco
import mujoco.viewer
import numpy as np
import time
import math

class MuJoCoLearningDemo:
    """
    MuJoCo 学习演示类 - 包含各种功能来帮助学习 MuJoCo
    """
    
    def __init__(self, xml_path='mujoco_menagerie/universal_robots_ur5e/ur5e.xml'):
        """初始化模型和数据"""
        print("🤖 初始化 MuJoCo 学习演示...")
        
        # 加载模型
        self.model = mujoco.MjModel.from_xml_path(xml_path)
        self.data = mujoco.MjData(self.model)
        
        # 演示模式
        self.demo_mode = 0
        self.demo_modes = [
            "基础信息显示",
            "正弦波控制",
            "随机运动",
            "重力补偿",
            "关节空间控制",
            "末端执行器控制",
            "碰撞检测演示",
            "传感器数据读取",
            "约束演示",
            "能量分析"
        ]
        
        # 控制参数
        self.time_step = 0
        self.control_freq = 0.5  # 控制频率
        
        # 数据记录
        self.position_history = []
        self.velocity_history = []
        self.force_history = []
        
        self.print_model_info()
    
    def print_model_info(self):
        """打印模型基础信息"""
        print("\n" + "="*60)
        print("📊 模型信息概览")
        print("="*60)
        print(f"模型名称: {self.model.names[0] if self.model.names else '未命名'}")
        print(f"关节数量: {self.model.nq}")
        print(f"驱动器数量: {self.model.nu}")
        print(f"刚体数量: {self.model.nbody}")
        print(f"几何体数量: {self.model.ngeom}")
        print(f"传感器数量: {self.model.nsensor}")
        print(f"时间步长: {self.model.opt.timestep}")
        
        print("\n📝 关节信息:")
        for i in range(self.model.nq):
            joint_name = mujoco.mj_id2name(self.model, mujoco.mjtObj.mjOBJ_JOINT, i)
            joint_type = self.model.jnt_type[i]
            print(f"  关节 {i}: {joint_name} (类型: {joint_type})")
        
        if self.model.nu > 0:
            print("\n🎮 驱动器信息:")
            for i in range(self.model.nu):
                actuator_name = mujoco.mj_id2name(self.model, mujoco.mjtObj.mjOBJ_ACTUATOR, i)
                print(f"  驱动器 {i}: {actuator_name}")
        
        print("="*60)
    
    def basic_info_display(self):
        """演示模式 0: 基础信息显示"""
        if self.time_step % 100 == 0:  # 每100步显示一次
            print(f"\n⏰ 时间: {self.data.time:.2f}s")
            print(f"📍 关节位置: {np.round(self.data.qpos, 3)}")
            print(f"💨 关节速度: {np.round(self.data.qvel, 3)}")
            if self.model.nu > 0:
                print(f"🔧 控制输入: {np.round(self.data.ctrl, 3)}")
            print(f"⚡ 系统能量: {self.calculate_total_energy():.3f} J")
    
    def sine_wave_control(self):
        """演示模式 1: 正弦波控制"""
        if self.model.nu > 0:
            # 为每个驱动器生成不同频率的正弦波
            for i in range(min(self.model.nu, self.model.nq)):
                frequency = 0.5 + i * 0.2  # 不同关节使用不同频率
                amplitude = 0.5  # 振幅
                self.data.ctrl[i] = amplitude * math.sin(frequency * self.data.time)
            
            if self.time_step % 50 == 0:
                print(f"🌊 正弦波控制 - 时间: {self.data.time:.2f}s")
                print(f"   控制信号: {np.round(self.data.ctrl, 3)}")
    
    def random_motion(self):
        """演示模式 2: 随机运动"""
        if self.model.nu > 0 and self.time_step % 20 == 0:  # 每20步更新一次
            # 生成随机控制信号
            for i in range(self.model.nu):
                self.data.ctrl[i] = np.random.uniform(-1.0, 1.0)
            
            if self.time_step % 100 == 0:
                print(f"🎲 随机运动 - 控制信号: {np.round(self.data.ctrl, 3)}")
    
    def gravity_compensation(self):
        """演示模式 3: 重力补偿"""
        if self.model.nu > 0:
            # 计算重力补偿力矩
            mujoco.mj_inverse(self.model, self.data)
            gravity_compensation = self.data.qfrc_inverse.copy()
            
            # 应用重力补偿
            self.data.ctrl[:len(gravity_compensation)] = gravity_compensation[:self.model.nu]
            
            if self.time_step % 100 == 0:
                print(f"🪐 重力补偿 - 补偿力矩: {np.round(gravity_compensation[:self.model.nu], 3)}")
    
    def joint_space_control(self):
        """演示模式 4: 关节空间位置控制"""
        if self.model.nu > 0:
            # 目标关节位置（缓慢变化的正弦波）
            target_positions = []
            for i in range(min(self.model.nq, self.model.nu)):
                target_pos = 0.3 * math.sin(0.3 * self.data.time + i)
                target_positions.append(target_pos)
            
            # 简单PD控制
            kp = 10.0  # 比例增益
            kd = 1.0   # 微分增益
            
            for i in range(len(target_positions)):
                error_pos = target_positions[i] - self.data.qpos[i]
                error_vel = 0 - self.data.qvel[i]  # 目标速度为0
                self.data.ctrl[i] = kp * error_pos + kd * error_vel
            
            if self.time_step % 100 == 0:
                print(f"🎯 关节空间控制")
                print(f"   目标位置: {np.round(target_positions, 3)}")
                print(f"   当前位置: {np.round(self.data.qpos[:len(target_positions)], 3)}")
    
    def end_effector_control(self):
        """演示模式 5: 末端执行器控制"""
        if self.model.nu > 0:
            # 获取末端执行器位置
            end_effector_id = self.model.nbody - 1  # 假设最后一个刚体是末端执行器
            end_effector_pos = self.data.xpos[end_effector_id].copy()
            
            # 目标位置（圆形轨迹）
            radius = 0.1
            center = np.array([0.5, 0.0, 0.5])
            target_pos = center + radius * np.array([
                math.cos(0.5 * self.data.time),
                math.sin(0.5 * self.data.time),
                0
            ])
            
            # 计算雅可比矩阵
            jacp = np.zeros((3, self.model.nv))
            mujoco.mj_jac(self.model, self.data, jacp, None, end_effector_pos, end_effector_id)
            
            # 位置误差
            pos_error = target_pos - end_effector_pos
            
            # 伪逆解算
            if self.model.nu <= self.model.nv:
                jacp_pinv = np.linalg.pinv(jacp[:, :self.model.nu])
                joint_velocities = jacp_pinv @ (10.0 * pos_error)  # 比例控制
                
                # 速度到力矩的转换（简化）
                self.data.ctrl[:len(joint_velocities)] = joint_velocities
            
            if self.time_step % 100 == 0:
                print(f"🤖 末端执行器控制")
                print(f"   目标位置: {np.round(target_pos, 3)}")
                print(f"   当前位置: {np.round(end_effector_pos, 3)}")
                print(f"   位置误差: {np.round(pos_error, 3)}")
    
    def collision_detection_demo(self):
        """演示模式 6: 碰撞检测演示"""
        if self.data.ncon > 0:
            print(f"💥 检测到 {self.data.ncon} 个碰撞接触")
            for i in range(min(self.data.ncon, 5)):  # 最多显示5个接触
                contact = self.data.contact[i]
                print(f"   接触 {i}: 几何体1={contact.geom1}, 几何体2={contact.geom2}")
                print(f"   接触位置: {np.round(contact.pos, 3)}")
                print(f"   接触力: {np.round(contact.frame, 3)}")
        elif self.time_step % 200 == 0:
            print("🟢 无碰撞检测")
    
    def sensor_data_reading(self):
        """演示模式 7: 传感器数据读取"""
        if self.model.nsensor > 0:
            if self.time_step % 100 == 0:
                print(f"📡 传感器数据读取 ({self.model.nsensor} 个传感器)")
                for i in range(self.model.nsensor):
                    sensor_name = mujoco.mj_id2name(self.model, mujoco.mjtObj.mjOBJ_SENSOR, i)
                    sensor_type = self.model.sensor_type[i]
                    sensor_data = self.data.sensordata[self.model.sensor_adr[i]:
                                                     self.model.sensor_adr[i] + self.model.sensor_dim[i]]
                    print(f"   {sensor_name}: {np.round(sensor_data, 3)} (类型: {sensor_type})")
        else:
            if self.time_step % 200 == 0:
                print("📡 模型中无传感器")
    
    def constraint_demo(self):
        """演示模式 8: 约束演示"""
        # 显示约束信息
        if self.time_step % 150 == 0:
            print(f"🔗 约束信息:")
            print(f"   等式约束数: {self.model.neq}")
            print(f"   不等式约束数: {self.model.nineq}")
            
            if self.data.nefc > 0:
                print(f"   当前约束力: {np.round(self.data.efc_force[:min(5, self.data.nefc)], 3)}")
    
    def energy_analysis(self):
        """演示模式 9: 能量分析"""
        if self.time_step % 100 == 0:
            kinetic_energy = self.calculate_kinetic_energy()
            potential_energy = self.calculate_potential_energy()
            total_energy = kinetic_energy + potential_energy
            
            print(f"⚡ 能量分析:")
            print(f"   动能: {kinetic_energy:.4f} J")
            print(f"   势能: {potential_energy:.4f} J")
            print(f"   总能量: {total_energy:.4f} J")
            
            # 记录数据用于可能的分析
            self.position_history.append(self.data.qpos.copy())
            self.velocity_history.append(self.data.qvel.copy())
    
    def calculate_kinetic_energy(self):
        """计算系统动能"""
        # 计算质量矩阵
        M = np.zeros((self.model.nv, self.model.nv))
        mujoco.mj_fullM(self.model, M, self.data.qM)
        
        # 动能 = 0.5 * v^T * M * v
        kinetic_energy = 0.5 * self.data.qvel.T @ M @ self.data.qvel
        return kinetic_energy
    
    def calculate_potential_energy(self):
        """计算势能（重力势能）"""
        potential_energy = 0.0
        for i in range(self.model.nbody):
            mass = self.model.body_mass[i]
            height = self.data.xpos[i][2]  # Z坐标
            potential_energy += mass * 9.81 * height
        return potential_energy
    
    def calculate_total_energy(self):
        """计算总能量"""
        return self.calculate_kinetic_energy() + self.calculate_potential_energy()
    
    def switch_demo_mode(self):
        """切换演示模式"""
        self.demo_mode = (self.demo_mode + 1) % len(self.demo_modes)
        print(f"\n🔄 切换到演示模式 {self.demo_mode}: {self.demo_modes[self.demo_mode]}")
        print("="*60)
    
    def print_controls_info(self):
        """打印控制说明"""
        print("\n" + "="*60)
        print("🎮 控制说明")
        print("="*60)
        print("鼠标控制:")
        print("  左键拖拽: 旋转视角")
        print("  右键拖拽: 平移视角")
        print("  滚轮: 缩放")
        print("\n键盘控制:")
        print("  Space: 暂停/继续仿真")
        print("  R: 重置仿真")
        print("  TAB: 切换演示模式")
        print("  Ctrl+R: 重新加载模型")
        print("="*60)
    
    def run_simulation(self):
        """运行仿真主循环"""
        self.print_controls_info()
        
        # 启动查看器
        with mujoco.viewer.launch_passive(self.model, self.data) as viewer:
            # 设置键盘回调
            last_tab_time = 0
            
            print(f"\n🚀 开始仿真 - 当前模式: {self.demo_modes[self.demo_mode]}")
            
            while viewer.is_running():
                # 检查TAB键切换模式
                current_time = time.time()
                if viewer.is_running() and hasattr(viewer, '_key_tab') and current_time - last_tab_time > 0.5:
                    # 这里简化处理，实际项目中可能需要更复杂的键盘事件处理
                    pass
                
                # 根据当前演示模式执行相应功能
                if self.demo_mode == 0:
                    self.basic_info_display()
                elif self.demo_mode == 1:
                    self.sine_wave_control()
                elif self.demo_mode == 2:
                    self.random_motion()
                elif self.demo_mode == 3:
                    self.gravity_compensation()
                elif self.demo_mode == 4:
                    self.joint_space_control()
                elif self.demo_mode == 5:
                    self.end_effector_control()
                elif self.demo_mode == 6:
                    self.collision_detection_demo()
                elif self.demo_mode == 7:
                    self.sensor_data_reading()
                elif self.demo_mode == 8:
                    self.constraint_demo()
                elif self.demo_mode == 9:
                    self.energy_analysis()
                
                # 仿真步进
                mujoco.mj_step(self.model, self.data)
                viewer.sync()
                
                self.time_step += 1
                
                # 自动切换演示模式（可选）
                if self.time_step % 1000 == 0:  # 每1000步自动切换
                    self.switch_demo_mode()


def main():
    """主函数"""
    print("🎯 MuJoCo 学习演示程序")
    print("这个程序包含了多种演示模式来帮助学习 MuJoCo 的各种功能")
    
    # 可以尝试不同的模型
    available_models = [
        'mujoco_menagerie/universal_robots_ur5e/ur5e.xml',
        'mujoco_menagerie/franka_emika_panda/panda.xml',
        'mujoco_menagerie/unitree_go1/go1.xml',
        'mujoco_menagerie/kinova_gen3/gen3.xml'
    ]
    
    # 使用第一个可用的模型
    model_path = available_models[0]
    
    try:
        # 创建并运行演示
        demo = MuJoCoLearningDemo(model_path)
        demo.run_simulation()
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        print("请确保模型文件存在且MuJoCo正确安装")


if __name__ == "__main__":
    main()
