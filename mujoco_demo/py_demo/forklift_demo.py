"""
叉车模型 MuJoCo 演示脚本
加载和控制 forklift.xml 模型
"""

import mujoco
import mujoco.viewer
import numpy as np
import math
import os

def forklift_basic_demo():
    """叉车基础演示"""
    print("🚛 叉车模型基础演示")
    
    # 获取当前脚本目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # 构建模型路径
    model_path = os.path.join(current_dir, '..', 'forklift.xml')
    
    try:
        # 加载叉车模型
        model = mujoco.MjModel.from_xml_path(model_path)
        data = mujoco.MjData(model)
        
        print(f"✅ 叉车模型加载成功")
        print(f"📊 模型信息:")
        print(f"   - 刚体数量: {model.nbody}")
        print(f"   - 关节数量: {model.nq}")
        print(f"   - 驱动器数量: {model.nu}")
        print(f"   - 几何体数量: {model.ngeom}")
        
        # 打印刚体信息
        print(f"🔧 刚体列表:")
        for i in range(model.nbody):
            body_name = mujoco.mj_id2name(model, mujoco.mjtObj.mjOBJ_BODY, i)
            if body_name:
                mass = model.body_mass[i]
                print(f"   - {body_name}: 质量 {mass:.2f} kg")
        
        # 启动可视化
        print("🎮 启动可视化...")
        print("💡 提示: 使用鼠标拖拽旋转视角，滚轮缩放")
        
        with mujoco.viewer.launch_passive(model, data) as viewer:
            step = 0
            while viewer.is_running():
                # 基础重力仿真，无控制输入
                if model.nu > 0:
                    data.ctrl[:] = 0.0
                
                # 每1000步打印状态信息
                if step % 1000 == 0:
                    print(f"⏰ 步数: {step}, 仿真时间: {data.time:.2f}s")
                    
                    # 打印主要刚体位置
                    for i in range(model.nbody):
                        body_name = mujoco.mj_id2name(model, mujoco.mjtObj.mjOBJ_BODY, i)
                        if body_name and body_name in ['carbody', 'fork_left', 'fork_right']:
                            pos = data.xpos[i]
                            print(f"   📍 {body_name}: ({pos[0]:.2f}, {pos[1]:.2f}, {pos[2]:.2f})")
                
                # 仿真步进
                mujoco.mj_step(model, data)
                viewer.sync()
                step += 1
                
    except Exception as e:
        print(f"❌ 加载模型失败: {e}")
        print("请确保 forklift.xml 文件存在且格式正确")
        return False
    
    return True

def forklift_oscillation_demo():
    """叉车振荡演示 - 如果有驱动器的话"""
    print("🔄 叉车振荡演示")
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    model_path = os.path.join(current_dir, '..', 'forklift.xml')
    
    try:
        model = mujoco.MjModel.from_xml_path(model_path)
        data = mujoco.MjData(model)
        
        if model.nu == 0:
            print("⚠️  该模型没有驱动器，将进行静态展示")
            return forklift_basic_demo()
        
        print(f"🎯 发现 {model.nu} 个驱动器，开始振荡控制")
        
        with mujoco.viewer.launch_passive(model, data) as viewer:
            step = 0
            while viewer.is_running():
                # 对所有驱动器施加正弦波控制
                for i in range(model.nu):
                    amplitude = 0.2  # 控制幅度
                    frequency = 0.005  # 控制频率
                    phase = i * math.pi / model.nu  # 相位差
                    data.ctrl[i] = amplitude * math.sin(frequency * step + phase)
                
                if step % 1000 == 0:
                    print(f"🎮 控制信号: {np.round(data.ctrl, 3)}")
                
                mujoco.mj_step(model, data)
                viewer.sync()
                step += 1
                
    except Exception as e:
        print(f"❌ 演示失败: {e}")

def forklift_analysis():
    """叉车模型分析"""
    print("🔍 叉车模型分析")
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    model_path = os.path.join(current_dir, '..', 'forklift.xml')
    
    try:
        model = mujoco.MjModel.from_xml_path(model_path)
        data = mujoco.MjData(model)
        
        print("📋 详细模型信息:")
        print(f"{'='*50}")
        print(f"模型名称: {model.name}")
        print(f"时间步长: {model.opt.timestep} 秒")
        print(f"重力: {model.opt.gravity}")
        
        print(f"\n🏗️  结构信息:")
        print(f"   刚体数量: {model.nbody}")
        print(f"   关节数量: {model.njnt}")
        print(f"   几何体数量: {model.ngeom}")
        print(f"   网格数量: {model.nmesh}")
        print(f"   驱动器数量: {model.nu}")
        
        print(f"\n📦 刚体详情:")
        total_mass = 0
        for i in range(model.nbody):
            name = mujoco.mj_id2name(model, mujoco.mjtObj.mjOBJ_BODY, i)
            mass = model.body_mass[i]
            total_mass += mass
            if name:
                print(f"   {i}: {name} - 质量: {mass:.2f} kg")
        
        print(f"\n⚖️  总质量: {total_mass:.2f} kg")
        
        print(f"\n🔧 几何体详情:")
        for i in range(model.ngeom):
            name = mujoco.mj_id2name(model, mujoco.mjtObj.mjOBJ_GEOM, i)
            geom_type = model.geom_type[i]
            if name:
                type_names = {0: 'plane', 1: 'hfield', 2: 'sphere', 3: 'capsule', 
                             4: 'ellipsoid', 5: 'cylinder', 6: 'box', 7: 'mesh'}
                type_name = type_names.get(geom_type, f'type_{geom_type}')
                print(f"   {i}: {name} - 类型: {type_name}")
        
        if model.nmesh > 0:
            print(f"\n🎨 网格文件:")
            for i in range(model.nmesh):
                name = mujoco.mj_id2name(model, mujoco.mjtObj.mjOBJ_MESH, i)
                if name:
                    print(f"   {i}: {name}")
        
        # 计算模型边界
        mujoco.mj_forward(model, data)
        
        print(f"\n📏 空间信息:")
        if model.nbody > 0:
            min_pos = np.min(data.xpos, axis=0)
            max_pos = np.max(data.xpos, axis=0)
            size = max_pos - min_pos
            print(f"   边界框: {min_pos} 到 {max_pos}")
            print(f"   尺寸: {size}")
        
        print(f"{'='*50}")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")

def check_model_files():
    """检查模型文件完整性"""
    print("🔎 检查模型文件...")
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    model_path = os.path.join(current_dir, '..', 'forklift.xml')
    
    # 检查XML文件
    if os.path.exists(model_path):
        print(f"✅ 找到模型文件: {model_path}")
    else:
        print(f"❌ 未找到模型文件: {model_path}")
        return False
    
    # 检查网格目录
    mesh_dir = os.path.join(current_dir, '..', 'meshes', 'new')
    if os.path.exists(mesh_dir):
        print(f"✅ 找到网格目录: {mesh_dir}")
        
        # 列出网格文件
        mesh_files = ['fork_main.stl', 'jia_left.stl', 'jia_right.stl']
        for mesh_file in mesh_files:
            mesh_path = os.path.join(mesh_dir, mesh_file)
            if os.path.exists(mesh_path):
                size = os.path.getsize(mesh_path)
                print(f"   ✅ {mesh_file} ({size} bytes)")
            else:
                print(f"   ❌ 缺失: {mesh_file}")
    else:
        print(f"⚠️  网格目录不存在: {mesh_dir}")
        print("   模型可能无法正确显示")
    
    # 检查纹理目录
    texture_dir = os.path.join(current_dir, '..', 'textures')
    if os.path.exists(texture_dir):
        print(f"✅ 找到纹理目录: {texture_dir}")
    else:
        print(f"⚠️  纹理目录不存在: {texture_dir}")
    
    return True

def interactive_forklift_menu():
    """交互式叉车演示菜单"""
    demos = {
        '1': ('基础演示', forklift_basic_demo),
        '2': ('振荡演示', forklift_oscillation_demo),
        '3': ('模型分析', forklift_analysis),
        '4': ('文件检查', check_model_files)
    }
    
    print("\n🚛 叉车模型 MuJoCo 演示程序")
    print("="*50)
    print("请选择演示:")
    for key, (name, _) in demos.items():
        print(f"  {key}. {name}")
    print("  q. 退出")
    print("="*50)
    
    while True:
        try:
            choice = input("\n请输入选择 (1-4/q): ").strip().lower()
            
            if choice == 'q':
                print("👋 再见!")
                break
            elif choice in demos:
                name, func = demos[choice]
                print(f"\n🚀 启动 {name}...")
                func()
            else:
                print("❌ 无效选择，请重试")
        
        except KeyboardInterrupt:
            print("\n👋 程序被中断，退出")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")

if __name__ == "__main__":
    # 首先检查文件
    if check_model_files():
        interactive_forklift_menu()
    else:
        print("❌ 文件检查失败，请确保模型文件完整")
