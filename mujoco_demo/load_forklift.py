"""
叉车模型加载脚本
加载并显示 forklift.xml 模型
"""

import mujoco
import mujoco.viewer
import os

def main():
    """加载并显示叉车模型"""
    print("🚛 加载叉车模型...")
    
    # 获取模型文件路径
    model_path = os.path.join(os.path.dirname(__file__), '..', 'forklift', 'forklift.xml')
    
    try:
        # 加载模型
        model = mujoco.MjModel.from_xml_path(model_path)
        data = mujoco.MjData(model)
        
        print("✅ 模型加载成功!")
        print(f"📊 刚体数量: {model.nbody}")
        print(f"📊 关节数量: {model.nq}")
        print(f"📊 驱动器数量: {model.nu}")
        
        # 启动可视化
        print("🎮 启动 MuJoCo 可视化...")
        print("💡 使用鼠标拖拽旋转视角，滚轮缩放，ESC 退出")
        
        with mujoco.viewer.launch_passive(model, data) as viewer:
            while viewer.is_running():
                # 基础物理仿真步进
                mujoco.mj_step(model, data)
                viewer.sync()
                
    except FileNotFoundError:
        print(f"❌ 找不到模型文件: {model_path}")
        print("请确保 forklift.xml 文件存在")
    except Exception as e:
        print(f"❌ 加载失败: {e}")
        print("请检查模型文件格式")

if __name__ == "__main__":
    main()
