import mujoco
import mujoco.viewer
import time

# 加载模型
m = mujoco.MjModel.from_xml_path('forklift/forklift.xml')
d = mujoco.MjData(m)

# 启动查看器
with mujoco.viewer.launch_passive(m, d) as viewer:
  # 模拟循环
  while viewer.is_running():
    step_start = time.time()
    mujoco.mj_step(m, d)
    viewer.sync()
    time_until_next_step = m.opt.timestep - (time.time() - step_start)
    if time_until_next_step > 0:
      time.sleep(time_until_next_step)