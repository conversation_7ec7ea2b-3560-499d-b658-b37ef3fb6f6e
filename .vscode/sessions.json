{"$schema": "https://cdn.statically.io/gh/nguyenngoclongdev/cdn/main/schema/v11/terminal-keeper.json", "theme": "tribe", "active": "default", "activateOnStartup": true, "keepExistingTerminals": false, "sessions": {"saved-session": [{"name": "run", "autoExecuteCommands": true, "icon": "person", "color": "terminal.ansiGreen", "commands": ["/home/<USER>/isaac/python.sh /home/<USER>/robot/isaac/control_forklift.py"]}, {"name": "agv复杂运动", "autoExecuteCommands": true, "icon": "person", "color": "terminal.ansiGreen", "commands": ["/home/<USER>/isaac/python.sh /home/<USER>/robot/isaac/control_forklift2.py"]}, {"name": "run c", "autoExecuteCommands": true, "icon": "person", "color": "terminal.ansiGreen", "commands": ["/home/<USER>/isaac/python.sh /home/<USER>/robot/isaac/control_forklift_c.py"]}]}}