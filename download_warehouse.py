import omni.client
import os

def download_asset(server_path, local_path):
    """Downloads an asset from an Omniverse Nucleus server."""
    print(f"Attempting to download from: {server_path}")
    print(f"Attempting to save to: {local_path}")

    # Ensure the local directory exists
    local_dir = os.path.dirname(local_path)
    if not os.path.exists(local_dir):
        os.makedirs(local_dir)
        print(f"Created directory: {local_dir}")

    try:
        # Connect and copy
        result, _ = omni.client.copy(server_path, local_path)

        if result == omni.client.Result.OK:
            print(f"Successfully downloaded: {local_path}")
            return True
        else:
            print(f"Failed to download. Result: {result}")
            return False
    except Exception as e:
        print(f"An error occurred: {e}")
        return False

if __name__ == "__main__":
    # Path on the public NVIDIA Omniverse Nucleus server
    nucleus_server = "omniverse://localhost/NVIDIA"
    warehouse_path = f"{nucleus_server}/Assets/Isaac/2023.1.1/Isaac/Environments/Simple_Warehouse/warehouse.usd"

    # Define a local path to save the asset
    local_assets_dir = os.path.join(os.getcwd(), "isaac_assets")
    local_warehouse_path = os.path.join(local_assets_dir, "Isaac/Environments/Simple_Warehouse/warehouse.usd")

    print("--- Starting Warehouse Asset Download ---")
    if download_asset(warehouse_path, local_warehouse_path):
        print("\n--- Download Complete ---")
        print(f"The warehouse.usd file and its dependencies should now be in: {os.path.dirname(local_warehouse_path)}")
        print("\nPlease update your control script to use this local path if needed.")
    else:
        print("\n--- Download Failed ---")
        print("Please ensure you are logged into the Omniverse Launcher and that the Nucleus service is running.")
